// 导航栏切换功能
document.addEventListener('DOMContentLoaded', function() {
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    navToggle.addEventListener('click', function() {
        navMenu.classList.toggle('active');
    });

    // 点击导航链接时关闭菜单
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function() {
            navMenu.classList.remove('active');
        });
    });

    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // 在线估价功能
    const quoteForm = document.getElementById('quoteForm');
    const quoteResult = document.getElementById('quoteResult');
    
    // 金属价格数据（每公斤价格范围）
    const metalPrices = {
        copper: { min: 52, max: 58, name: '废铜' },
        iron: { min: 2.8, max: 3.5, name: '废铁' },
        aluminum: { min: 12, max: 16, name: '废铝' },
        stainless: { min: 8, max: 12, name: '不锈钢' },
        other: { min: 1, max: 5, name: '其他金属' }
    };

    quoteForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(quoteForm);
        const metalType = formData.get('metalType');
        const weight = parseFloat(formData.get('weight'));
        const description = formData.get('description');
        const contact = formData.get('contact');
        const location = formData.get('location');
        
        // 验证表单
        if (!metalType || !weight || !contact || !location) {
            alert('请填写所有必填项！');
            return;
        }
        
        if (weight <= 0) {
            alert('请输入正确的重量！');
            return;
        }
        
        // 验证手机号格式
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(contact)) {
            alert('请输入正确的手机号码！');
            return;
        }
        
        // 计算估价
        const priceData = metalPrices[metalType];
        if (priceData) {
            const avgPrice = (priceData.min + priceData.max) / 2;
            const estimatedValue = (avgPrice * weight).toFixed(2);
            
            // 显示结果
            const estimatedPriceElement = document.querySelector('.estimated-price');
            estimatedPriceElement.textContent = `￥${estimatedValue}`;
            
            quoteResult.style.display = 'block';
            quoteResult.scrollIntoView({ behavior: 'smooth' });
            
            // 模拟提交到后端
            console.log('提交的估价信息:', {
                metalType: priceData.name,
                weight: weight,
                description: description,
                contact: contact,
                location: location,
                estimatedValue: estimatedValue
            });
            
            // 显示成功消息
            setTimeout(() => {
                alert('估价信息已提交，我们将在30分钟内联系您确认具体价格！');
            }, 1000);
        }
    });

    // 导航栏滚动效果
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 50) {
            navbar.style.background = 'rgba(44, 62, 80, 0.95)';
        } else {
            navbar.style.background = '#2c3e50';
        }
    });

    // 数字动画效果
    function animateNumbers() {
        const priceElements = document.querySelectorAll('.price');
        
        priceElements.forEach(element => {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        element.style.animation = 'pulse 0.5s ease-in-out';
                        observer.unobserve(entry.target);
                    }
                });
            });
            
            observer.observe(element);
        });
    }

    // 添加脉冲动画CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    `;
    document.head.appendChild(style);

    // 初始化数字动画
    animateNumbers();

    // 表单验证增强
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            clearErrors(this);
        });
    });

    function validateField(field) {
        const value = field.value.trim();
        clearErrors(field);
        
        if (field.hasAttribute('required') && !value) {
            showError(field, '此字段为必填项');
            return false;
        }
        
        if (field.type === 'number' && value && parseFloat(value) <= 0) {
            showError(field, '请输入正确的数值');
            return false;
        }
        
        if (field.name === 'contact' && value) {
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(value)) {
                showError(field, '请输入正确的手机号码');
                return false;
            }
        }
        
        return true;
    }

    function showError(field, message) {
        const errorElement = document.createElement('span');
        errorElement.className = 'field-error';
        errorElement.textContent = message;
        errorElement.style.color = '#e74c3c';
        errorElement.style.fontSize = '0.8rem';
        errorElement.style.marginTop = '0.25rem';
        
        field.style.borderColor = '#e74c3c';
        field.parentNode.appendChild(errorElement);
    }

    function clearErrors(field) {
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
        field.style.borderColor = '#ddd';
    }

    // 页面加载动画
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'fadeInUp 0.6s ease-out forwards';
            }
        });
    }, observerOptions);

    // 观察需要动画的元素
    document.querySelectorAll('.metal-card, .step, .contact-item').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        observer.observe(el);
    });

    // 添加淡入动画
    const fadeInStyle = document.createElement('style');
    fadeInStyle.textContent = `
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    `;
    document.head.appendChild(fadeInStyle);
});