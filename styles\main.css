/* 重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏样式 */
.navbar {
    background: #2c3e50;
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #27ae60;
}

.nav-logo i {
    margin-right: 0.5rem;
    font-size: 2rem;
}

.nav-menu {
    display: flex;
    list-style: none;
}

.nav-item {
    margin-left: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    transition: color 0.3s;
}

.nav-link:hover {
    color: #27ae60;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: white;
    margin: 3px 0;
    transition: 0.3s;
}

/* 主页部分 */
.hero {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    padding: 120px 0 80px;
    display: flex;
    align-items: center;
    min-height: 100vh;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-content h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: bold;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: #fff;
    color: #27ae60;
}

.btn-primary:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #27ae60;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.metal-icons {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.metal-icons i {
    font-size: 4rem;
    opacity: 0.3;
    animation: float 3s ease-in-out infinite;
}

.metal-icons i:nth-child(2) {
    animation-delay: 1s;
}

.metal-icons i:nth-child(3) {
    animation-delay: 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* 金属价格部分 */
.metals-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.metals-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.metals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.metal-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.metal-card:hover {
    transform: translateY(-5px);
}

.metal-icon {
    font-size: 3rem;
    color: #27ae60;
    margin-bottom: 1rem;
}

.metal-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.price {
    font-size: 1.8rem;
    font-weight: bold;
    color: #e74c3c;
    margin-bottom: 1rem;
}

.metal-types {
    list-style: none;
}

.metal-types li {
    padding: 0.3rem 0;
    color: #666;
}

.price-note {
    text-align: center;
    color: #666;
    font-style: italic;
}

.price-note i {
    margin-right: 0.5rem;
    color: #27ae60;
}

/* 在线估价部分 */
.quote-section {
    padding: 80px 0;
    background: white;
}

.quote-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.quote-form-container {
    max-width: 800px;
    margin: 0 auto;
}

.quote-form {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #27ae60;
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.quote-result {
    background: #27ae60;
    color: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    margin-top: 2rem;
}

.quote-result h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.estimated-price {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.result-note {
    opacity: 0.8;
    font-style: italic;
}

/* 服务流程部分 */
.process-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.process-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.process-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1000px;
    margin: 0 auto;
    flex-wrap: wrap;
}

.step {
    text-align: center;
    flex: 1;
    min-width: 150px;
    margin: 1rem 0;
}

.step-icon {
    width: 80px;
    height: 80px;
    background: #27ae60;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 2rem;
}

.step h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.step p {
    color: #666;
    font-size: 0.9rem;
}

.step-arrow {
    color: #27ae60;
    font-size: 1.5rem;
    margin: 0 1rem;
}

/* 联系我们部分 */
.contact-section {
    padding: 80px 0;
    background: white;
}

.contact-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.contact-item {
    display: flex;
    align-items: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 10px;
    transition: transform 0.3s;
}

.contact-item:hover {
    transform: translateY(-5px);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: #27ae60;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.contact-details h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.contact-details p {
    color: #666;
    margin: 0.2rem 0;
}

/* 页脚 */
.footer {
    background: #2c3e50;
    color: white;
    padding: 2rem 0;
    text-align: center;
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #27ae60;
}

.footer-logo i {
    margin-right: 0.5rem;
    font-size: 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: #2c3e50;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-item {
        margin: 1rem 0;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .process-steps {
        flex-direction: column;
    }

    .step-arrow {
        transform: rotate(90deg);
        margin: 1rem 0;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
    }

    .contact-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }
}